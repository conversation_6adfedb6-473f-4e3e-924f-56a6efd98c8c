# AgentFlow - Visual Architecture Diagrams & Blueprints

This document contains detailed visual diagrams and architectural blueprints that complement the main technical documentation.

## System Architecture Blueprints

### 1. Complete System Overview

```mermaid
graph TB
    subgraph "Client Tier"
        WEB[Web Browser]
        MOBILE[Mobile Browser]
    end
    
    subgraph "Edge/CDN Tier"
        EDGE[Vercel Edge Network]
        CACHE[Global Cache]
    end
    
    subgraph "Application Tier"
        subgraph "Next.js Application"
            FRONTEND[React Frontend]
            API[API Routes]
            MIDDLEWARE[Middleware]
        end
        
        subgraph "Orchestration Engine"
            LANGGRAPH[LangGraph State Machine]
            EXECUTOR[Node Executors]
            ROUTER[Flow Router]
        end
    end
    
    subgraph "Integration Tier"
        subgraph "AI/ML Services"
            OPENAI[OpenAI GPT-4]
            ANTHROPIC[Claude 3.5]
            GOOGLE[Gemini Pro]
        end
        
        subgraph "Tool Platform"
            COMPOSIO[Composio SDK]
            TOOLS[External Tools]
        end
    end
    
    subgraph "Data Tier"
        subgraph "Primary Storage"
            SUPABASE[(Supabase PostgreSQL)]
            AUTH[Supabase Auth]
            STORAGE[File Storage]
        end
        
        subgraph "Caching Layer"
            REDIS[(Redis Cache)]
            MEMORY[In-Memory Cache]
        end
    end
    
    subgraph "Monitoring & Ops"
        LOGS[Centralized Logging]
        METRICS[Metrics Collection]
        ALERTS[Alert System]
    end
    
    %% Client connections
    WEB --> EDGE
    MOBILE --> EDGE
    
    %% Edge to application
    EDGE --> FRONTEND
    EDGE --> API
    
    %% Application internal flow
    FRONTEND --> API
    API --> MIDDLEWARE
    MIDDLEWARE --> LANGGRAPH
    LANGGRAPH --> EXECUTOR
    EXECUTOR --> ROUTER
    
    %% Integration connections
    EXECUTOR --> OPENAI
    EXECUTOR --> ANTHROPIC
    EXECUTOR --> GOOGLE
    EXECUTOR --> COMPOSIO
    COMPOSIO --> TOOLS
    
    %% Data connections
    API --> SUPABASE
    API --> AUTH
    API --> STORAGE
    EXECUTOR --> REDIS
    EXECUTOR --> MEMORY
    
    %% Monitoring connections
    API --> LOGS
    EXECUTOR --> METRICS
    METRICS --> ALERTS
    
    %% Styling
    classDef clientTier fill:#e1f5fe
    classDef edgeTier fill:#f3e5f5
    classDef appTier fill:#e8f5e8
    classDef integrationTier fill:#fff3e0
    classDef dataTier fill:#fce4ec
    classDef monitoringTier fill:#f1f8e9
    
    class WEB,MOBILE clientTier
    class EDGE,CACHE edgeTier
    class FRONTEND,API,MIDDLEWARE,LANGGRAPH,EXECUTOR,ROUTER appTier
    class OPENAI,ANTHROPIC,GOOGLE,COMPOSIO,TOOLS integrationTier
    class SUPABASE,AUTH,STORAGE,REDIS,MEMORY dataTier
    class LOGS,METRICS,ALERTS monitoringTier
```

### 2. Detailed Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API Gateway
    participant M as Middleware
    participant L as LangGraph
    participant E as Executors
    participant AI as AI Services
    participant T as Tools
    participant D as Database
    participant C as Cache
    
    Note over U,C: Workflow Creation & Execution Flow
    
    %% Workflow Creation
    U->>F: Create/Edit Workflow
    F->>A: POST /api/flows
    A->>M: Validate Request
    M->>D: Store Workflow
    D-->>M: Confirmation
    M-->>A: Success Response
    A-->>F: Workflow Saved
    F-->>U: UI Update
    
    Note over U,C: Workflow Execution Flow
    
    %% Workflow Execution
    U->>F: Execute Workflow
    F->>A: POST /api/agent
    A->>M: Authenticate & Validate
    M->>C: Check Cache
    
    alt Cache Hit
        C-->>M: Cached Result
        M-->>A: Return Cached
    else Cache Miss
        M->>L: Initialize State Machine
        L->>E: Start Execution
        
        loop For Each Node
            E->>E: Determine Node Type
            
            alt LLM Node
                E->>AI: Send Prompt
                AI-->>E: AI Response
            else Agent Node
                E->>AI: Get Tool Decision
                AI-->>E: Tool Selection
                E->>T: Execute Tool
                T-->>E: Tool Result
            else Input/Output Node
                E->>E: Process Data
            end
            
            E->>L: Update State
            L->>E: Route to Next Node
        end
        
        L-->>M: Final Result
        M->>C: Cache Result
        M->>D: Log Execution
    end
    
    M-->>A: Execution Complete
    A-->>F: Return Result
    F-->>U: Display Output
```

### 3. Component Interaction Blueprint

```mermaid
graph LR
    subgraph "Frontend Components"
        BUILDER[Flow Builder]
        DASHBOARD[Dashboard]
        NODES[Node Components]
        UI[UI Components]
    end
    
    subgraph "API Layer"
        AGENT_API[Agent API]
        FLOW_API[Flow API]
        TOOLS_API[Tools API]
        AUTH_API[Auth API]
    end
    
    subgraph "Business Logic"
        VALIDATOR[Input Validator]
        ORCHESTRATOR[Flow Orchestrator]
        EXECUTOR[Node Executor]
        SERIALIZER[Graph Serializer]
    end
    
    subgraph "External Integrations"
        LLM_PROVIDERS[LLM Providers]
        COMPOSIO_PLATFORM[Composio Platform]
        SUPABASE_SERVICES[Supabase Services]
    end
    
    %% Frontend to API connections
    BUILDER --> AGENT_API
    BUILDER --> FLOW_API
    BUILDER --> TOOLS_API
    DASHBOARD --> FLOW_API
    DASHBOARD --> AUTH_API
    NODES --> TOOLS_API
    UI --> AUTH_API
    
    %% API to Business Logic connections
    AGENT_API --> VALIDATOR
    AGENT_API --> ORCHESTRATOR
    FLOW_API --> SERIALIZER
    TOOLS_API --> EXECUTOR
    
    %% Business Logic internal connections
    VALIDATOR --> ORCHESTRATOR
    ORCHESTRATOR --> EXECUTOR
    EXECUTOR --> SERIALIZER
    
    %% Business Logic to External connections
    ORCHESTRATOR --> LLM_PROVIDERS
    EXECUTOR --> LLM_PROVIDERS
    EXECUTOR --> COMPOSIO_PLATFORM
    SERIALIZER --> SUPABASE_SERVICES
    VALIDATOR --> SUPABASE_SERVICES
```

## Detailed Component Diagrams

### 4. Node Execution Engine

```mermaid
stateDiagram-v2
    [*] --> Initialized
    
    Initialized --> ValidatingInput: Receive Graph
    ValidatingInput --> InputValid: Validation Success
    ValidatingInput --> InputInvalid: Validation Failed
    InputInvalid --> [*]: Return Error
    
    InputValid --> CreatingState: Initialize LangGraph State
    CreatingState --> StateCreated: State Ready
    
    StateCreated --> ExecutingNodes: Start Execution
    
    state ExecutingNodes {
        [*] --> FindingNextNode
        FindingNextNode --> NodeFound: Node Available
        FindingNextNode --> ExecutionComplete: No More Nodes
        
        NodeFound --> DeterminingType: Analyze Node Type
        
        state DeterminingType {
            [*] --> InputNode
            [*] --> LLMNode
            [*] --> AgentNode
            [*] --> OutputNode
            
            InputNode --> ProcessingInput
            LLMNode --> CallingLLM
            AgentNode --> ExecutingAgent
            OutputNode --> FormattingOutput
        }
        
        ProcessingInput --> UpdatingState
        CallingLLM --> UpdatingState
        ExecutingAgent --> UpdatingState
        FormattingOutput --> UpdatingState
        
        UpdatingState --> FindingNextNode: Continue
        
        ExecutionComplete --> [*]
    }
    
    ExecutingNodes --> ReturningResult: Execution Done
    ReturningResult --> [*]: Complete
```

### 5. Authentication & Authorization Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant F as Frontend
    participant A as Auth API
    participant S as Supabase Auth
    participant D as Database
    participant P as Protected Resource
    
    Note over C,P: User Authentication Flow
    
    C->>F: Login Request
    F->>A: POST /auth/login
    A->>S: Authenticate User
    S-->>A: JWT Token + User Info
    A->>D: Store Session Info
    A-->>F: Return Token
    F->>F: Store Token (HttpOnly Cookie)
    F-->>C: Login Success
    
    Note over C,P: Protected Resource Access
    
    C->>F: Request Protected Resource
    F->>P: API Call with JWT
    P->>S: Validate JWT Token
    
    alt Valid Token
        S-->>P: User Info
        P->>D: Check User Permissions
        D-->>P: Permission Result
        
        alt Authorized
            P->>P: Process Request
            P-->>F: Return Data
            F-->>C: Display Resource
        else Unauthorized
            P-->>F: 403 Forbidden
            F-->>C: Access Denied
        end
    else Invalid Token
        S-->>P: Token Invalid
        P-->>F: 401 Unauthorized
        F->>F: Clear Token
        F-->>C: Redirect to Login
    end
```

### 6. Database Schema Relationships

```mermaid
erDiagram
    USERS {
        uuid id PK
        varchar email UK
        varchar encrypted_password
        timestamp email_confirmed_at
        timestamp created_at
        timestamp updated_at
        jsonb raw_app_meta_data
        jsonb raw_user_meta_data
    }
    
    FLOWS {
        uuid id PK
        uuid user_id FK
        varchar name
        text description
        jsonb graph_json
        varchar status
        timestamp created_at
        timestamp updated_at
        timestamp last_executed_at
        integer execution_count
    }
    
    EXECUTIONS {
        uuid id PK
        uuid flow_id FK
        uuid user_id FK
        jsonb input_data
        jsonb output_data
        jsonb execution_steps
        varchar status
        integer duration_ms
        timestamp started_at
        timestamp completed_at
        text error_message
    }
    
    API_KEYS {
        uuid id PK
        uuid user_id FK
        varchar provider
        varchar encrypted_key
        varchar key_name
        boolean is_active
        timestamp created_at
        timestamp last_used_at
    }
    
    USAGE_METRICS {
        uuid id PK
        uuid user_id FK
        uuid flow_id FK
        varchar metric_type
        integer metric_value
        jsonb metadata
        timestamp recorded_at
    }
    
    USERS ||--o{ FLOWS : creates
    USERS ||--o{ EXECUTIONS : runs
    USERS ||--o{ API_KEYS : owns
    USERS ||--o{ USAGE_METRICS : generates
    FLOWS ||--o{ EXECUTIONS : has
    FLOWS ||--o{ USAGE_METRICS : tracks
```

## Infrastructure Diagrams

### 7. Deployment Architecture

```mermaid
graph TB
    subgraph "Internet"
        USERS[Users]
        BOTS[Bots/Crawlers]
    end
    
    subgraph "Vercel Edge Network"
        EDGE1[Edge Location 1]
        EDGE2[Edge Location 2]
        EDGE3[Edge Location N]
        CDN[Global CDN]
    end
    
    subgraph "Vercel Serverless"
        subgraph "Production Environment"
            PROD_APP[Production App]
            PROD_API[Production API]
            PROD_FUNC[Serverless Functions]
        end
        
        subgraph "Preview Environment"
            PREV_APP[Preview App]
            PREV_API[Preview API]
        end
    end
    
    subgraph "Supabase Cloud"
        subgraph "Database Cluster"
            PRIMARY_DB[(Primary Database)]
            REPLICA_DB[(Read Replica)]
            BACKUP_DB[(Backup Storage)]
        end
        
        subgraph "Auth Service"
            AUTH_SVC[Authentication]
            USER_MGMT[User Management]
        end
        
        subgraph "Storage Service"
            FILE_STORAGE[File Storage]
            ASSET_CDN[Asset CDN]
        end
    end
    
    subgraph "External APIs"
        OPENAI_API[OpenAI API]
        ANTHROPIC_API[Anthropic API]
        GOOGLE_API[Google AI API]
        COMPOSIO_API[Composio API]
    end
    
    subgraph "Monitoring Stack"
        VERCEL_ANALYTICS[Vercel Analytics]
        SUPABASE_METRICS[Supabase Metrics]
        CUSTOM_LOGS[Custom Logging]
    end
    
    %% User connections
    USERS --> EDGE1
    USERS --> EDGE2
    USERS --> EDGE3
    BOTS --> CDN
    
    %% Edge to application
    EDGE1 --> PROD_APP
    EDGE2 --> PROD_APP
    EDGE3 --> PROD_APP
    CDN --> PROD_APP
    
    %% Application internal
    PROD_APP --> PROD_API
    PROD_API --> PROD_FUNC
    
    %% Preview environment
    EDGE1 --> PREV_APP
    PREV_APP --> PREV_API
    
    %% Database connections
    PROD_API --> PRIMARY_DB
    PROD_API --> REPLICA_DB
    PRIMARY_DB --> BACKUP_DB
    
    %% Auth connections
    PROD_API --> AUTH_SVC
    AUTH_SVC --> USER_MGMT
    
    %% Storage connections
    PROD_APP --> FILE_STORAGE
    FILE_STORAGE --> ASSET_CDN
    
    %% External API connections
    PROD_FUNC --> OPENAI_API
    PROD_FUNC --> ANTHROPIC_API
    PROD_FUNC --> GOOGLE_API
    PROD_FUNC --> COMPOSIO_API
    
    %% Monitoring connections
    PROD_APP --> VERCEL_ANALYTICS
    PRIMARY_DB --> SUPABASE_METRICS
    PROD_API --> CUSTOM_LOGS
```

### 8. Security Architecture

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Network Security"
            WAF[Web Application Firewall]
            DDOS[DDoS Protection]
            TLS[TLS/SSL Encryption]
        end
        
        subgraph "Application Security"
            AUTH[JWT Authentication]
            AUTHZ[Authorization Middleware]
            VALIDATION[Input Validation]
            SANITIZATION[Data Sanitization]
        end
        
        subgraph "Data Security"
            ENCRYPTION[Data Encryption]
            RLS[Row Level Security]
            BACKUP[Encrypted Backups]
            AUDIT[Audit Logging]
        end
        
        subgraph "API Security"
            RATE_LIMIT[Rate Limiting]
            CORS[CORS Policy]
            API_KEYS[API Key Management]
            MONITORING[Security Monitoring]
        end
    end
    
    subgraph "Threat Vectors"
        INJECTION[SQL Injection]
        XSS[Cross-Site Scripting]
        CSRF[CSRF Attacks]
        BRUTE_FORCE[Brute Force]
        DATA_BREACH[Data Breaches]
    end
    
    subgraph "Security Controls"
        PARAM_QUERIES[Parameterized Queries]
        CSP[Content Security Policy]
        CSRF_TOKENS[CSRF Tokens]
        ACCOUNT_LOCKOUT[Account Lockout]
        ENCRYPTION_REST[Encryption at Rest]
    end
    
    %% Security mappings
    INJECTION --> PARAM_QUERIES
    XSS --> CSP
    CSRF --> CSRF_TOKENS
    BRUTE_FORCE --> ACCOUNT_LOCKOUT
    DATA_BREACH --> ENCRYPTION_REST
    
    %% Layer protections
    WAF --> INJECTION
    VALIDATION --> XSS
    AUTH --> BRUTE_FORCE
    ENCRYPTION --> DATA_BREACH
    RATE_LIMIT --> DDOS
```

## Performance & Scaling Diagrams

### 9. Caching Strategy

```mermaid
graph LR
    subgraph "Request Flow"
        CLIENT[Client Request]
        CDN[CDN Cache]
        APP[Application]
        MEMORY[Memory Cache]
        REDIS[Redis Cache]
        DATABASE[(Database)]
    end
    
    subgraph "Cache Levels"
        L1[L1: Browser Cache]
        L2[L2: CDN Cache]
        L3[L3: Memory Cache]
        L4[L4: Redis Cache]
        L5[L5: Database Cache]
    end
    
    subgraph "Cache Policies"
        STATIC[Static Assets: 1 year]
        API[API Responses: 5 minutes]
        USER[User Data: 1 hour]
        FLOWS[Flow Data: 30 minutes]
        RESULTS[Execution Results: 24 hours]
    end
    
    %% Request flow
    CLIENT --> CDN
    CDN --> APP
    APP --> MEMORY
    MEMORY --> REDIS
    REDIS --> DATABASE
    
    %% Cache level mapping
    CLIENT -.-> L1
    CDN -.-> L2
    MEMORY -.-> L3
    REDIS -.-> L4
    DATABASE -.-> L5
    
    %% Policy application
    L2 -.-> STATIC
    L3 -.-> API
    L4 -.-> USER
    L4 -.-> FLOWS
    L4 -.-> RESULTS
```

### 10. Auto-Scaling Architecture

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Vercel Load Balancer]
        HEALTH[Health Checks]
    end
    
    subgraph "Application Instances"
        INSTANCE1[Instance 1]
        INSTANCE2[Instance 2]
        INSTANCE3[Instance 3]
        INSTANCEN[Instance N]
    end
    
    subgraph "Scaling Triggers"
        CPU[CPU Usage > 70%]
        MEMORY[Memory Usage > 80%]
        REQUESTS[Request Rate > 1000/min]
        RESPONSE[Response Time > 2s]
    end
    
    subgraph "Scaling Actions"
        SCALE_UP[Scale Up]
        SCALE_DOWN[Scale Down]
        HEALTH_CHECK[Health Check]
        TERMINATE[Terminate Unhealthy]
    end
    
    subgraph "Metrics Collection"
        METRICS[Metrics Collector]
        ALERTS[Alert Manager]
        DASHBOARD[Monitoring Dashboard]
    end
    
    %% Load balancer connections
    LB --> INSTANCE1
    LB --> INSTANCE2
    LB --> INSTANCE3
    LB --> INSTANCEN
    
    %% Health monitoring
    HEALTH --> INSTANCE1
    HEALTH --> INSTANCE2
    HEALTH --> INSTANCE3
    HEALTH --> INSTANCEN
    
    %% Scaling triggers
    CPU --> SCALE_UP
    MEMORY --> SCALE_UP
    REQUESTS --> SCALE_UP
    RESPONSE --> SCALE_UP
    
    %% Scaling actions
    SCALE_UP --> INSTANCEN
    SCALE_DOWN --> TERMINATE
    HEALTH_CHECK --> TERMINATE
    
    %% Metrics flow
    INSTANCE1 --> METRICS
    INSTANCE2 --> METRICS
    INSTANCE3 --> METRICS
    INSTANCEN --> METRICS
    METRICS --> ALERTS
    METRICS --> DASHBOARD
```

This comprehensive set of visual diagrams and architectural blueprints provides detailed insights into every aspect of the AgentFlow system, from high-level architecture to specific implementation details, security measures, and operational procedures.