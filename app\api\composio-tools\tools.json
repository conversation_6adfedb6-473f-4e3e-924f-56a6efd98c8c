{"items": [{"slug": "gmail", "name": "Gmail", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gmail.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 1, "tools_count": 21, "description": "Connect to Gmail to send and manage emails.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gmail.svg", "app_url": "https://mail.google.com", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "composio", "name": "Composio", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//composio-logo.png", "authConfigMode": ["NO_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": true, "meta": {"triggers_count": 0, "tools_count": 12, "description": "Composio enables AI Agents and LLMs to authenticate and integrate with various tools via function calling.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//composio-logo.png", "app_url": "https://composio.dev", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["NO_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": true}, {"slug": "github", "name": "GitHub", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/github.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 6, "tools_count": 906, "description": "GitHub API Tool", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/github.png", "app_url": "https://github.com/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "googlecalendar", "name": "Googlecalendar", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-calendar.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 1, "tools_count": 12, "description": "Google Calendar is a time-management and scheduling calendar service developed by Google.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-calendar.svg", "app_url": "https://calendar.google.com", "categories": [{"id": "scheduling-&-booking", "name": "scheduling & booking"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "notion", "name": "Notion", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/notion.svg", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 3, "tools_count": 23, "description": "Notion centralizes notes, docs, wikis, and tasks in a unified workspace, letting teams build custom workflows for collaboration and knowledge management", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/notion.svg", "app_url": "https://www.notion.so", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "googlesheets", "name": "Googlesheets", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-sheets.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 9, "description": "Google Sheets is a web-based spreadsheet program that is part of the\n    Google Drive office suite.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-sheets.svg", "app_url": "https://sheets.google.com/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "slack", "name": "<PERSON><PERSON>ck", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/slack.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 8, "tools_count": 174, "description": "Slack is a channel-based messaging platform. With Slack, people can work together more effectively, connect all their software tools and services, and find the information they need to do their best work — all within a secure, enterprise-grade environment.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/slack.svg", "app_url": "https://slack.com/intl/en-in//", "categories": [{"id": "productivity", "name": "productivity"}, {"id": "popular", "name": "popular"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "supabase", "name": "Supabase", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/supabase.jpeg", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 77, "description": "Supabase is an open-source backend-as-a-service providing a Postgres database, authentication, storage, and real-time subscription APIs for building modern applications", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/supabase.jpeg", "app_url": "https://supabase.com", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "outlook", "name": "Outlook", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Outlook%20SVG%20Icon.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 5, "tools_count": 22, "description": "Outlook is Microsoft’s email and calendaring platform integrating contacts, tasks, and scheduling, enabling users to manage communications and events in a unified workspace", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Outlook%20SVG%20Icon.svg", "app_url": "https://www.microsoft.com/en-in/microsoft-365/outlook/email-and-calendar-software-microsoft-outlook", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "perplexityai", "name": "Perplexityai", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//perplexity.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "The Perplexity tool interfaces with Perplexity AI's search service, offering advanced natural language processing for sophisticated searches. The PerplexityAISearch action allows for:\n\n    - Query execution with AI models tailored to various search tasks.\n    - Search customization through parameters affecting content generation.\n    - Domain filtering to enhance search precision.\n    - Real-time response streaming for dynamic applications.\n\n    This action is highly configurable for a tailored search experience, suitable for a wide range of AI-driven applications.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//perplexity.jpeg", "app_url": "https://perplexity.ai", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "twitter", "name": "Twitter", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/twitter.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 72, "description": "Twitter, Inc. was an American social media company based in San Francisco, California, which operated and was named for named for its flagship social media network prior to its rebrand as X.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/twitter.png", "app_url": "https://x.com", "categories": [{"id": "social", "name": "social"}, {"id": "marketing", "name": "marketing"}, {"id": "popular", "name": "popular"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "googledrive", "name": "Googledrive", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-drive.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 1, "tools_count": 10, "description": "Connect to Google Drive!", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-drive.svg", "app_url": "https://drive.google.com", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "googledocs", "name": "Googledocs", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-docs.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 5, "description": "Connect to Google Docs to perform various document-related actions.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-docs.svg", "app_url": "https://docs.google.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}, {"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "hubspot", "name": "Hubspot", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//hubspot.webp", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 2, "tools_count": 229, "description": "HubSpot is an inbound marketing, sales, and customer service platform integrating CRM, email automation, and analytics to facilitate lead nurturing and seamless customer experiences", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//hubspot.webp", "app_url": "https://www.hubspot.com/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}, {"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "linear", "name": "Linear", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linear.png", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 3, "tools_count": 18, "description": "Connect to Linear to create and manage issues, list projects, teams, and more", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linear.png", "app_url": "https://linear.app", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "airtable", "name": "Airtable", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/airtable.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 17, "description": "Airtable is a low‒code platform to build next‒gen apps. Move beyond\n    rigid tools, operationalize your critical data, and reimagine\n    workflows with AI", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/airtable.svg", "app_url": "https://www.airtable.com/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}, {"id": "workflow-automation", "name": "workflow automation"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "codeinterpreter", "name": "Codeinterpreter", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//code-interpreter.webp", "authConfigMode": ["NO_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": true, "meta": {"triggers_count": 0, "tools_count": 4, "description": "CodeInterpreter extends Python-based coding environments with integrated data analysis, enabling developers to run scripts, visualize results, and prototype solutions inside supported platforms", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//code-interpreter.webp", "app_url": "...", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}, {"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["NO_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": true}, {"slug": "serp<PERSON>i", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//serpapi.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 10, "description": "SerpApi provides a real-time API for structured search engine results, allowing developers to scrape, parse, and analyze SERP data for SEO and research", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//serpapi.png", "app_url": "https://serpapi.com/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "jira", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/jira.svg", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 3, "tools_count": 41, "description": "Jira API tool", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/jira.svg", "app_url": "https://www.atlassian.com/software/jira", "categories": [{"id": "productivity", "name": "productivity"}, {"id": "ticketing", "name": "ticketing"}, {"id": "popular", "name": "popular"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "firecrawl", "name": "Firecrawl", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/firecrawl.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 7, "description": "Firecrawl automates web crawling and data extraction, enabling organizations to gather content, index sites, and gain insights from online sources at scale", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/firecrawl.jpeg", "app_url": "https://www.firecrawl.dev/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "tavily", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/tavily.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "Tavily provides advanced search capabilities with various options\n    including image inclusion, raw content, and domain filtering.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/tavily.svg", "app_url": "​https://tavily.com", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "youtube", "name": "Youtube", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/youtube.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 4, "tools_count": 11, "description": "Youtube actions to interact with youtube app", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/youtube.svg", "app_url": "https://www.youtube.com", "categories": [{"id": "entertainment-&-media", "name": "entertainment & media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "slackbot", "name": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/slack.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 8, "tools_count": 174, "description": "Slackbot automates responses and reminders within Slack, assisting with tasks like onboarding, FAQs, and notifications to streamline team productivity", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/slack.svg", "app_url": "https://slack.com/intl/en-in/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "canvas", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/canvas.jpeg", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 80, "description": "Canvas is a learning management system supporting online courses, assignments, grading, and collaboration, widely used by schools and universities for virtual classrooms", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/canvas.jpeg", "app_url": "https://www.instructure.com/canvas", "categories": [{"id": "education-&-lms", "name": "education & lms"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "bitbucket", "name": "Bitbucket", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/bitbucket.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 15, "description": "Bitbucket is a Git-based code hosting and collaboration platform supporting private and public repositories, enabling teams to manage and review code through pull requests and integrations", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/bitbucket.svg", "app_url": "https://bitbucket.org/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "googletasks", "name": "Googletasks", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//google-tasks.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 11, "description": "Google Tasks provides a simple to-do list and task management system integrated into Gmail and Google Calendar for quick and easy tracking", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//google-tasks.png", "app_url": "https://tasks.google.com/tasks/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "discord", "name": "Discord", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/discord.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 1, "tools_count": 169, "description": "An instant messaging and VoIP social platform.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/discord.svg", "app_url": "​https://discord.com", "categories": [{"id": "gaming", "name": "gaming"}, {"id": "social", "name": "social"}, {"id": "popular", "name": "popular"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "figma", "name": "Figma", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/figma.svg", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 44, "description": "A collaborative interface design tool.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/figma.svg", "app_url": "https://www.figma.com/", "categories": [{"id": "design-&-creative-tools", "name": "design & creative tools"}, {"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "composio_search", "name": "Composio search", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//composio-logo.png", "authConfigMode": ["NO_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": true, "meta": {"triggers_count": 0, "tools_count": 12, "description": "Composio Search is an all-in-one tool for searching and scraping the web.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//composio-logo.png", "app_url": "https://composio.dev", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["NO_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": true}, {"slug": "reddit", "name": "Reddit", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/reddit.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 9, "description": "Connect to Reddit to post and comment.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/reddit.svg", "app_url": "https://www.reddit.com", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}, {"id": "entertainment-&-media", "name": "entertainment & media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "cal", "name": "Cal", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//cal-logo.png", "authConfigMode": ["API_KEY", "CALCOM_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 142, "description": "Cal simplifies meeting coordination by providing shareable booking pages, calendar syncing, and availability management to streamline the scheduling process", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//cal-logo.png", "app_url": "https://cal.com", "categories": [{"id": "scheduling-&-booking", "name": "scheduling & booking"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY", "CALCOM_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "wrike", "name": "<PERSON><PERSON>e", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/wrike.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 38, "description": "Wrike is a project management and collaboration tool offering customizable workflows, Gantt charts, reporting, and resource management to boost team productivity", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/wrike.png", "app_url": "https://www.wrike.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "exa", "name": "Exa", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//exa.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 4, "description": "The Exa class extends the base Tool class to interact with the Exa Search service, offering actions like Search, Similarlink, and Answer.\n    These actions enable querying, finding similar links, and generating answers from search results. Currently, no triggers are defined, but they can be added as needed to enhance functionality.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//exa.png", "app_url": "https://exa.ai", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "sentry", "name": "Sentry", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sentry.svg", "authConfigMode": ["BEARER_TOKEN"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 177, "description": "Integrate Sentry to manage your error tracking and monitoring.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sentry.svg", "app_url": "https://sentry.io/welcome/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["BEARER_TOKEN"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "snowflake", "name": "Snowflake", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/snowflake.svg", "authConfigMode": ["BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 4, "description": "Connect to Snowflake to run queries.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/snowflake.svg", "app_url": "https://www.snowflake.com/en/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "hackernews", "name": "Hackernews", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/hackernews.png", "authConfigMode": ["NO_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": true, "meta": {"triggers_count": 0, "tools_count": 6, "description": "Hacker News is a tech-focused news aggregator by Y Combinator, featuring user-submitted stories and discussions on startups, programming, and emerging trends", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/hackernews.png", "app_url": "https://news.ycombinator.com/", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["NO_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": true}, {"slug": "elevenlabs", "name": "Elevenlabs", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/elevenlabs.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 59, "description": "Create natural AI voices instantly in any language - perfect for video creators, developers, and businesses.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/elevenlabs.png", "app_url": "https://www.elevenlabs.io", "categories": [{"id": "voice", "name": "voice"}, {"id": "popular", "name": "popular"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "microsoft_teams", "name": "Microsoft teams", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//microsoft-teams-logo.jpeg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 13, "description": "Connect to Microsoft Teams to manage channels.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//microsoft-teams-logo.jpeg", "app_url": "https://www.microsoft.com/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "asana", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//asana.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 1, "tools_count": 15, "description": "Tool to help teams organize, track, and manage their work.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//asana.png", "app_url": "https://asana.com/", "categories": [{"id": "productivity", "name": "productivity"}, {"id": "ticketing", "name": "ticketing"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "peopledatalabs", "name": "Peopledatalabs", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pdl.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 14, "description": "PeopleDataLabs provides B2B data enrichment and identity resolution, empowering organizations to build enriched user profiles and validate customer information", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pdl.png", "app_url": "https://www.peopledatalabs.com/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "shopify", "name": "Shopify", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/shopify.svg", "authConfigMode": ["API_KEY", "OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 26, "description": "A leading global commerce platform that allows anyone to sell online\n    and in person.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/shopify.svg", "app_url": "https://www.shopify.com", "categories": [{"id": "e-commerce", "name": "e-commerce"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY", "OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "linkedin", "name": "Linkedin", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linkedin.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 4, "description": "Connect to Linked to send and manage emails.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linkedin.svg", "app_url": "https://linkedin.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "google_maps", "name": "Google maps", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google_maps.jpeg", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 4, "description": "Google Maps is a web mapping platform and consumer application offering satellite imagery,\n    aerial photography, street maps, 360° interactive panoramic views of streets, real-time\n    traffic conditions, and route planning for traveling by foot, car, bike, air and public\n    transportation.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google_maps.jpeg", "app_url": "https://www.google.com/maps", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "one_drive", "name": "One drive", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/one-drive.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 4, "tools_count": 7, "description": "OneDrive is Microsoft’s cloud storage solution enabling users to store, sync, and share files across devices, offering offline access, real-time collaboration, and enterprise-grade security", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/one-drive.svg", "app_url": "https://www.microsoft.com/en-in/microsoft-365/onedrive/online-cloud-storage/", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "docusign", "name": "Docusign", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/docusign.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 342, "description": "DocuSign provides eSignature and digital agreement solutions, enabling businesses to send, sign, track, and manage documents electronically", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/docusign.svg", "app_url": "https://www.docusign.com", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "discordbot", "name": "Discordbot", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/discord.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 1, "tools_count": 169, "description": "Discordbot refers to automated programs on Discord servers, performing tasks like moderation, music playback, and user engagement to enhance community interactions", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/discord.svg", "app_url": "https://discord.com/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "salesforce", "name": "Salesforce", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/salesforce.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 2, "tools_count": 32, "description": "Salesforce is a leading CRM platform integrating sales, service, marketing, and analytics to build customer relationships and drive business growth", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/salesforce.svg", "app_url": "https://www.salesforce.com/in/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "twitter_media", "name": "Twitter media", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/twitter.png", "authConfigMode": ["OAUTH1"], "composioManagedAuthConfigs": ["OAUTH1"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "Twitter Media focuses on multimedia tools and features within Twitter, allowing brands to leverage rich content for marketing campaigns", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/twitter.png", "app_url": "https://x.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH1"], "composio_managed_auth_schemes": ["OAUTH1"], "is_local_toolkit": false, "no_auth": false}, {"slug": "calendly", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/calendly.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 41, "description": "Calendly is an appointment scheduling tool that automates meeting invitations, availability checks, and reminders, helping individuals and teams avoid email back-and-forth", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/calendly.svg", "app_url": "https://calendly.com/", "categories": [{"id": "scheduling-&-booking", "name": "scheduling & booking"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "trello", "name": "Trello", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/trello.svg", "authConfigMode": ["OAUTH1", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH1"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 4, "tools_count": 323, "description": "A web-based, kanban-style, list-making application.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/trello.svg", "app_url": "https://trello.com", "categories": [{"id": "productivity", "name": "productivity"}, {"id": "ticketing", "name": "ticketing"}, {"id": "popular", "name": "popular"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH1", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH1"], "is_local_toolkit": false, "no_auth": false}, {"slug": "apollo", "name": "Apollo", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/apollo.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 17, "description": "Apollo is a CRM tool that allows you to manage your contacts, leads, and opportunities.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/apollo.jpg", "app_url": "https://www.apollo.io/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "semrush", "name": "Semrush", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/semrush.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 36, "description": "Semrush is a popular SEO tool suite that specializes in keyword research, competitor analysis, and Google Ad campaign optimization.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/semrush.png", "app_url": "https://www.semrush.com/", "categories": [{"id": "seo", "name": "seo"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "mem0", "name": "Mem0", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//mem0.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 43, "description": "Mem0 assists with AI-driven note-taking, knowledge recall, and productivity tools, allowing users to organize, search, and generate content from stored information", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//mem0.png", "app_url": "https://mem0.ai/", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}, {"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "neon", "name": "Neon", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/neon.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 69, "description": "Postgres, on a serverless platform designed to help you build reliable and scalable applications faster", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/neon.png", "app_url": "https://neon.tech/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "weathermap", "name": "Weathermap", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/weathermap.png", "authConfigMode": ["NO_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": true, "meta": {"triggers_count": 0, "tools_count": 1, "description": "WeatherMap provides visual weather data, forecasts, and mappings, helping users understand climate patterns or track severe weather conditions", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/weathermap.png", "app_url": "https://openweathermap.org/weathermap", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["NO_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": true}, {"slug": "posthog", "name": "Posthog", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/posthog.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 358, "description": "PostHog is an open-source product analytics platform tracking user interactions and behaviors to help teams refine features, improve funnels, and reduce churn", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/posthog.svg", "app_url": "https://posthog.com/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "clickup", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/clickup.png", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 126, "description": "ClickUp unifies tasks, docs, goals, and chat in a single platform, allowing teams to plan, organize, and collaborate across projects with customizable workflows", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/clickup.png", "app_url": "https://clickup.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "brevo", "name": "Brevo", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//brevo-logo.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 221, "description": "Brevo (formerly Sendinblue) delivers email marketing, SMS campaigns, and marketing automation solutions, empowering businesses to nurture leads, engage audiences, and drive conversions", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//brevo-logo.jpg", "app_url": "https://www.brevo.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "stripe", "name": "Stripe", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/stripe.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 7, "tools_count": 33, "description": "Stripe offers online payment infrastructure, fraud prevention, and APIs enabling businesses to accept and manage payments globally", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/stripe.jpeg", "app_url": "https://stripe.com", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}, {"id": "e-commerce", "name": "e-commerce"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "klaviyo", "name": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//klaviyo.png", "authConfigMode": ["API_KEY", "OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 231, "description": "Klaviyo is a data-driven email and SMS marketing platform that allows e-commerce brands to deliver targeted messages, track conversions, and scale customer relationships", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//klaviyo.png", "app_url": "https://www.klaviyo.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY", "OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "browserbase_tool", "name": "Browserbase tool", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//browser-base.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "A browsing app that gets a URL, reads its contents, and returns it.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//browser-base.jpeg", "app_url": "https://browserbase.com", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "mailchimp", "name": "Mailchimp", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mailchimp.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 4, "tools_count": 271, "description": "Mailchimp is an email marketing and automation platform providing campaign templates, audience segmentation, and performance analytics to drive engagement and conversions", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mailchimp.svg", "app_url": "https://mailchimp.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "attio", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//attio.webp", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 56, "description": "Attio is a fully customizable workspace for your team's relationships and workflows.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//attio.webp", "app_url": "https://attio.com", "categories": [{"id": "crm", "name": "crm"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "goog<PERSON><PERSON><PERSON>", "name": "Googlemeet", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-meet.webp", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 5, "description": "Google Meet is a video conferencing tool developed by Google.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google-meet.webp", "app_url": "https://meet.google.com/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "text_to_pdf", "name": "Text to pdf", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/texttopdf.png", "authConfigMode": ["NO_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": true, "meta": {"triggers_count": 0, "tools_count": 1, "description": "Convert text to PDF", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/texttopdf.png", "app_url": "...", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["NO_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": true}, {"slug": "zoho", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zoho.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 6, "description": "Zoho actions to interact with zoho CRM", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zoho.png", "app_url": "https://www.zoho.com", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}, {"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "fireflies", "name": "Fireflies", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/fireflies.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 1, "tools_count": 10, "description": "Fireflies.ai helps your team transcribe, summarize, search, and analyze voice conversations.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/fireflies.jpg", "app_url": "https://www.fireflies.ai", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "dropbox", "name": "Dropbox", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/dropbox.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 9, "description": "Dropbox is a file hosting service that offers cloud storage, file\n    synchronization, personal cloud, and client software.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/dropbox.svg", "app_url": "https://www.dropbox.com", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "shortcut", "name": "Shortcut", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//shortcut.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 122, "description": "Shortcut aligns product development work with company objectives so teams can execute with a shared purpose.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//shortcut.svg", "app_url": "https://www.shortcut.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "confluence", "name": "Confluence", "enabled": true, "logo": "https://raw.githubusercontent.com/ComposioHQ/open-logos/refs/heads/master/confluence-svgrepo-com.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 190, "description": "A tool for team collaboration and knowledge management.", "logo": "https://raw.githubusercontent.com/ComposioHQ/open-logos/refs/heads/master/confluence-svgrepo-com.svg", "app_url": "https://www.atlassian.com/software/confluence", "categories": [{"id": "collaboration", "name": "collaboration"}, {"id": "knowledge-management", "name": "knowledge-management"}, {"id": "popular", "name": "popular"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "freshdesk", "name": "Freshdesk", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/freshdesk.svg", "authConfigMode": ["BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 7, "description": "A customer support platform that provides helpdesk support with all\n    smart automations to get things done faster.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/freshdesk.svg", "app_url": "https://www.freshworks.com/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "borneo", "name": "Borneo", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/borneo.jpeg", "authConfigMode": ["API_KEY", "OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 154, "description": "Borneo is a data security and privacy platform designed for sensitive data discovery and remediation.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/borneo.jpeg", "app_url": "https://www.borneo.com", "categories": [{"id": "security-&-compliance", "name": "security & compliance"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY", "OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "mixpanel", "name": "Mixpanel", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mixpanel.svg", "authConfigMode": ["BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 19, "description": "Mixpanel is an analytics platform that helps companies measure user\n    engagement and retention.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mixpanel.svg", "app_url": "https://mixpanel.com/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "coda", "name": "Coda", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/coda.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 100, "description": "Collaborative workspace platform that transforms documents into powerful tools for team productivity and project management", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/coda.png", "app_url": "https://coda.io/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "acculynx", "name": "Acculynx", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/acculynx.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 8, "description": "Using the AccuLynx API, data can be seamlessly exchanged between AccuLynx and other applications for greater efficiency and productivity.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/acculynx.jpeg", "app_url": "https://acculynx.com", "categories": [{"id": "crm", "name": "crm"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "ahrefs", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ahrefs.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 40, "description": "Ahrefs is an SEO and marketing platform offering site audits, keyword research, content analysis, and competitive insights to improve search rankings and drive organic traffic", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ahrefs.png", "app_url": "https://ahrefs.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "affinity", "name": "Affinity", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/affinity.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 20, "description": "Affinity helps private capital investors to find, manage, and close more deals", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/affinity.jpeg", "app_url": "https://www.affinity.co/", "categories": [{"id": "crm", "name": "crm"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "amplitude", "name": "Amplitude", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//amplitude.svg", "authConfigMode": ["BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 16, "description": "Amplitude Inc. is an American publicly trading company that develops digital analytics software.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//amplitude.svg", "app_url": "https://amplitude.com/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "heygen", "name": "Heygen", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/heygen.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 35, "description": "HeyGen is an innovative video platform that harnesses the power of generative AI to streamline your video creation process", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/heygen.jpg", "app_url": "https://www.heygen.com", "categories": [{"id": "ai-video", "name": "ai-video"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "agencyzoom", "name": "Agencyzoom", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/agencyzoom_logo.jpeg", "authConfigMode": ["API_KEY", "BASIC_WITH_JWT"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 99, "description": "AgencyZoom is for the P&C insurance agent that's looking to increase sales, boost retention and analyze agency & producer performance.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/agencyzoom_logo.jpeg", "app_url": "https://www.agencyzoom.com/", "categories": [{"id": "crm", "name": "crm"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY", "BASIC_WITH_JWT"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "googlebigquery", "name": "Googlebigquery", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/googl-bigquery.svg", "authConfigMode": ["GOOGLE_SERVICE_ACCOUNT"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "Connect to BigQuery to query BigData.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/googl-bigquery.svg", "app_url": "https://cloud.google.com/bigquery", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["GOOGLE_SERVICE_ACCOUNT"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "microsoft_clarity", "name": "Microsoft clarity", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//microsoft-clarity-logo.jpeg", "authConfigMode": ["BEARER_TOKEN"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "Microsoft Clarity is a free, easy-to-use tool that captures how real\n    people actually use your site.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//microsoft-clarity-logo.jpeg", "app_url": "https://clarity.microsoft.com", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["BEARER_TOKEN"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "coinbase", "name": "Coinbase", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/coinbase.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 6, "description": "Coinbase provides APIs for cryptocurrency trading and management.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/coinbase.svg", "app_url": "https://www.coinbase.com/en-in", "categories": [{"id": "cryptocurrency", "name": "cryptocurrency"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "monday", "name": "Monday", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//monday.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 21, "description": "Monday is a cloud-based work operating system where teams create\n    workflow apps in minutes to run their processes, projects, and\n    everyday work.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//monday.png", "app_url": "https://monday.com/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "semanticscholar", "name": "Semanticscholar", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/semanticscholar.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 14, "description": "Semantic Scholar is an AI-powered academic search engine that helps researchers discover and understand scientific literature", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/semanticscholar.png", "app_url": "https://www.semanticscholar.org", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "sendgrid", "name": "Sendgrid", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sendgrid.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 374, "description": "SendGrid is a cloud-based email delivery platform providing transactional and marketing email services, with APIs for integration, analytics, and scalability", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sendgrid.png", "app_url": "https://login.sendgrid.com", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "junglescout", "name": "Junglescout", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/junglescout.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 6, "description": "Jungle Scout assists Amazon sellers with product research, sales estimates, and competitive insights to optimize inventory, pricing, and listing strategies", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/junglescout.jpeg", "app_url": "https://www.junglescout.com", "categories": [{"id": "e-commerce", "name": "e-commerce"}, {"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "pipedrive", "name": "Pipedrive", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pipedrive.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 3, "tools_count": 276, "description": "Pipedrive is a sales management tool built around pipeline visualization, lead tracking, activity reminders, and automation to keep deals progressing", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pipedrive.svg", "app_url": "https://www.pipedrive.com/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "bamboohr", "name": "Bamboohr", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//bamboohr.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 159, "description": "BambooHR is an American technology company that provides human resources software as a service.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//bamboohr.svg", "app_url": "https://www.bamboohr.com", "categories": [{"id": "erp", "name": "erp"}, {"id": "crm", "name": "crm"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "whatsapp", "name": "Whatsapp", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//whatsapp.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 5, "description": "Enables interaction with customers through the WhatsApp Business API for messaging and automation.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//whatsapp.png", "app_url": "https://web.whatsapp.com/", "categories": [{"id": "communication", "name": "communication"}, {"id": "messaging", "name": "messaging"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "dynamics365", "name": "Dynamics365", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Dynamics%20365%20Icon.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 16, "description": "Dynamics 365 from Microsoft combines CRM, ERP, and productivity apps to streamline sales, marketing, customer service, and operations in one integrated platform", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Dynamics%20365%20Icon.svg", "app_url": "https://www.microsoft.com/en-in/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}, {"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "zendesk", "name": "Zendesk", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zendesk.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 2, "tools_count": 11, "description": "Zendesk is a customer service and sales platform that helps businesses manage customer communication and support", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zendesk.svg", "app_url": "https://www.zendesk.com/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "googlephotos", "name": "Googlephotos", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Google_Photos.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 12, "description": "Google Photos is a photo and video storage service that is part of the\n    Google Drive office suite.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Google_Photos.png", "app_url": "https://photos.google.com", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "lmnt", "name": "Lmnt", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/lmnt_logo.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 7, "description": "LMNT is an API for text-to-speech and voice cloning", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/lmnt_logo.jpeg", "app_url": "https://drinklmnt.com", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}, {"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "metaads", "name": "Metaads", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/meta-icon.svg", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 16, "description": "Meta Ads Marketing API Integration\n\n    This tool provides access to Meta's Marketing API for managing ad campaigns,\n    ad sets, ads, and custom audiences, as well as retrieving insights and analytics.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/meta-icon.svg", "app_url": "https://business.meta.com/", "categories": [{"id": "social-media", "name": "social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "zenrows", "name": "Zenrows", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zenrows.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 16, "description": "ZenRows is a web scraping API allowing developers to bypass CAPTCHAs and blocks, gather structured data from dynamic websites, and quickly integrate results into applications", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zenrows.jpeg", "app_url": "https://www.zenrows.com/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "googlesuper", "name": "Googlesuper", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google.svg", "authConfigMode": ["OAUTH2", "BEARER_TOKEN", "API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 3, "tools_count": 93, "description": "Google Super App combines all Google services including Drive, Calendar, Gmail, Sheets, Analytics, Ads, and more, providing a unified platform for seamless integration and management of your digital life.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/google.svg", "app_url": "https://www.google.com/", "categories": [{"id": "document-&-file-management", "name": "document & file management"}, {"id": "scheduling-&-booking", "name": "scheduling & booking"}, {"id": "collaboration-&-communication", "name": "collaboration & communication"}, {"id": "productivity-&-project-management", "name": "productivity & project management"}, {"id": "analytics-&-data", "name": "analytics & data"}, {"id": "advertising-&-marketing", "name": "advertising & marketing"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "BEARER_TOKEN", "API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "yousearch", "name": "Yousearch", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//you.webp", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "YouSearch is a search engine or search tool that enables users to find relevant information, possibly with enhanced filtering or privacy-focused features", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//you.webp", "app_url": "https://you.com/", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "linkup", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linkup.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 2, "description": "Search the Web for Relevant Results (RAG Use Case)", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linkup.jpeg", "app_url": "https://www.linkup.com/", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "listennotes", "name": "Listennotes", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/listennotes.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 26, "description": "The best podcast search engine.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/listennotes.png", "app_url": "https://www.listennotes.com", "categories": [{"id": "podcast", "name": "podcast"}, {"id": "productivity", "name": "productivity"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "typefully", "name": "Typefully", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/typefully.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 5, "description": "Integrate Typefully to streamline and manage your AI-powered content creation.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/typefully.png", "app_url": "https://typefully.com/", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "bolna", "name": "Bolna", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/bolna-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 15, "description": "Create conversational voice agents using Bolna AI to enhance interactions, streamline operations and automate support.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/bolna-logo.png", "app_url": "https://www.bolna.ai/", "categories": [{"id": "productivity", "name": "productivity"}, {"id": "dev-tools", "name": "dev-tools"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "rocketlane", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/rocketlane.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 6, "description": "Rocketlane specializes in customer onboarding and project delivery, providing shared workspaces, milestones, and status tracking to streamline implementations", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/rocketlane.png", "app_url": "https://www.rocketlane.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "bill", "name": "Bill", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/bill.jpeg", "authConfigMode": ["BILLCOM_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 221, "description": "Integration with Bill.com API", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/bill.jpeg", "app_url": "https://www.bill.com", "categories": [{"id": "accounting", "name": "accounting"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["BILLCOM_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "zoom", "name": "Zoom", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zoom.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 172, "description": "Zoom is a video conferencing and online meeting platform featuring breakout rooms, screen sharing, and integrations with various enterprise tools", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/zoom.svg", "app_url": "https://www.zoom.com/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.179Z", "updated_at": "2025-05-15T09:26:37.179Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "onepage", "name": "Onepage", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/onepage.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 2, "description": "API for enriching user and company data, providing endpoints for token validation and generic search.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/onepage.svg", "app_url": "https://onepagerapp.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}, {"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "entelligence", "name": "Entelligence", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/entelligence.png", "authConfigMode": ["NO_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": true, "meta": {"triggers_count": 0, "tools_count": 2, "description": "Entelligence leverages artificial intelligence to provide insights, recommendations, and predictive analytics for businesses seeking data-driven decision-making capabilities", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/entelligence.png", "app_url": "https://www.entelligence.ai/", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["NO_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": true}, {"slug": "retellai", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/retellai.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 10, "description": "RetellAI captures calls and transcripts, enabling businesses to analyze conversations, extract insights, and enhance customer interactions in one centralized platform", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/retellai.jpeg", "app_url": "https://www.retellai.com/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "servicenow", "name": "Servicenow", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/servicenow.png", "authConfigMode": ["BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 5, "description": "Servicenow provides IT Service Management Transform service management to boost productivity and maximize ROI", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/servicenow.png", "app_url": "https://www.servicenow.com", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "googleads", "name": "Googleads", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/googleads.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 4, "description": "Connect to Google Ads to manage and create campaigns.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/googleads.png", "app_url": "​https://ads.google.com", "categories": [{"id": "advertising-&-marketing", "name": "advertising & marketing"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pagerduty.png", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 357, "description": "Integrate PagerDuty to manage incidents, schedules, and alerts directly from your application.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pagerduty.png", "app_url": "https://www.pagerduty.com", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "toneden", "name": "Toneden", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//toneden-logo.jpeg", "authConfigMode": ["API_KEY", "OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "ToneDen provides APIs for automated marketing and social media\n    management.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//toneden-logo.jpeg", "app_url": "https://www.toneden.io/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY", "OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "rafflys", "name": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//rafflys.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Rafflys is a platform that allows you to create and manage giveaways\n    and contests. The Rafflys API allows developers to interact with the\n    platform programmatically.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//rafflys.png", "app_url": "https://app-sorteos.com/en", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "finage", "name": "Finage", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//finage.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Finage provides APIs for real-time and historical data on stocks,\n    forex, crypto, and company fundamentals.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//finage.png", "app_url": "https://finage.co.uk/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "fomo", "name": "Fomo", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//fomo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "FOMO provides APIs for social proof automation, showing recent\n    customer activity on websites.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//fomo.png", "app_url": "https://fomo.com", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "bannerbear", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//bannerbear-logo.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 8, "description": "Bannerbear offers an automated image and video generation API, allowing businesses to create graphics, social media visuals, and marketing collateral with customizable templates at scale", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//bannerbear-logo.jpg", "app_url": "https://www.bannerbear.com/", "categories": [{"id": "workflow-automation", "name": "workflow automation"}, {"id": "design-&-creative-tools", "name": "design & creative tools"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "miro", "name": "Mir<PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/miro.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Miro is a collaborative online whiteboard platform designed for remote\n    and distributed teams.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/miro.svg", "app_url": "https://miro.com/", "categories": [{"id": "design-&-creative-tools", "name": "design & creative tools"}, {"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "share_point", "name": "Share point", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sharepoint-icon.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 6, "description": "SharePoint is a Microsoft platform for document management and intranets, enabling teams to collaborate, store, and organize content securely and effectively", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sharepoint-icon.svg", "app_url": "https://www.microsoft.com/en-in/microsoft-365/sharepoint/collaboration/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "mocean", "name": "Mocean", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//mocean-api.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Mocean is a cloud communications platform that provides SMS, Voice,\n    and Verification APIs for developers and businesses.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//mocean-api.jpg", "app_url": "https://www.mocean.energy", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//formcarry.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Formcarry provides APIs for creating and managing forms, retrieving\n    form submissions, and handling notifications.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//formcarry.png", "app_url": "https://formcarry.com/", "categories": [{"id": "workflow-automation", "name": "workflow automation"}, {"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "appdrag", "name": "Appdrag", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/appdrag.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "AppDrag is a cloud development platform that offers tools for creating\n    professional websites and managing cloud backend services with ease.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/appdrag.svg", "app_url": "https://appdrag.com/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "metatextai", "name": "Metatextai", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//metatextai.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "MetaTextAI provides APIs for natural language processing, text\n    generation, and AI-powered content creation.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//metatextai.jpeg", "app_url": "https://metatext.ai", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "breezy_hr", "name": "Breezy hr", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/breezyhr.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Breezy HR to streamline your recruiting and applicant\n    tracking processes.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/breezyhr.jpg", "app_url": "https://breezy.hr/", "categories": [{"id": "hr-&-recruiting", "name": "hr & recruiting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "launch_darkly", "name": "Launch darkly", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//launch-darkly-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Control feature flags and manage your software releases with\n    LaunchDarkly.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//launch-darkly-logo.png", "app_url": "https://launchdarkly.com", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "mailerlite", "name": "Mailerlite", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//maintainx-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "MaintainX provides a comprehensive suite of tools for maintenance and\n    operations management, enabling businesses to streamline workflows,\n    enhance communication, and improve operational efficiency.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//maintainx-logo.png", "app_url": "https://www.mailerlite.com", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "mboum", "name": "Mboum", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//mboum.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Mboum provides APIs for real-time, intraday, and historical stock\n    market data, covering over 70+ stock exchanges and 185,000+ stock\n    tickers worldwide.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//mboum.jpeg", "app_url": "https://mboum.com", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "helcim", "name": "<PERSON>l<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//helcim.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Helcim provides APIs for payment processing, customer management, and\n    building custom payment solutions.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//helcim.png", "app_url": "https://www.helcim.com/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "contentful", "name": "Contentful", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//contentful-logo.png", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 3, "description": "Contentful provides a content platform with APIs for managing and\n    delivering content to apps and websites.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//contentful-logo.png", "app_url": "https://www.contentful.com/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "<PERSON><PERSON>b", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//oncehub.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "OnceHub provides APIs for scheduling, booking management, and customer\n    data integration.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//oncehub.jpg", "app_url": "https://oncehub.com", "categories": [{"id": "scheduling-&-booking", "name": "scheduling & booking"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "close", "name": "Close", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//close.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Close is a sales productivity platform fine-tuned to help SMBs turn\n    more leads into revenue", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//close.svg", "app_url": "https://www.close.com", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "icims_talent_cloud", "name": "Icims talent cloud", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/icims.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate iCIMS Talent Cloud to manage your talent acquisition,\n    recruitment processes, and candidate engagement.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/icims.png", "app_url": "https://www.icims.com/products/talent-cloud-recruiting/", "categories": [{"id": "hr-&-recruiting", "name": "hr & recruiting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "brex_staging", "name": "Brex staging", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//brex-staging-logo.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integration for Brex banking services in the staging environment using\n    OAuth2 authentication.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//brex-staging-logo.png", "app_url": "https://www.brex.com/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "do<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//docmosis.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Docmosis provides APIs for document generation, template management,\n    and data merging.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//docmosis.jpeg", "app_url": "https://www.docmosis.com", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "ably", "name": "Ably", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ably.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 11, "description": "Ably is a real-time messaging platform helping developers build live features, including chat and data synchronization, with global scalability and robust reliability for modern applications", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ably.svg", "app_url": "https://ably.com", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "more_trees", "name": "More trees", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//more-trees.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "More Trees is a sustainability-focused platform planting trees on behalf of individuals or businesses aiming to offset carbon footprints and support reforestation", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//more-trees.jpg", "app_url": "https://www.moretrees.eco/", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "netsuite", "name": "Netsuite", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//netsuite.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "NetSuite is a cloud-based suite of business management applications.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//netsuite.svg", "app_url": "https://www.netsuite.com", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "moz", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/moz.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Moz to access SEO tools, rank tracking, link building, and\n    more for optimizing your online presence.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/moz.png", "app_url": "https://moz.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/recall.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 8, "description": "Recall.ai provides a single API for meeting bots on every platform like Zoom, Google Meet, Microsoft Teams and more.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/recall.svg", "app_url": "https://www.recall.ai/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}, {"id": "e-commerce", "name": "e-commerce"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "apaleo", "name": "<PERSON>pal<PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/apaleo.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 29, "description": "Apaleo is a cloud-based property management platform handling reservations, billing, and daily operations for hospitality businesses", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/apaleo.png", "app_url": "https://apaleo.com​", "categories": [{"id": "scheduling-&-booking", "name": "scheduling & booking"}, {"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "survey_monkey", "name": "Survey monkey", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/surveymonkey.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "SurveyMonkey is an online survey software that helps with creating and\n    running surveys online.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/surveymonkey.png", "app_url": "https://www.surveymonkey.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}, {"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "zoho_books", "name": "Zoho books", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Zoho Books is online accounting software that manages your finances,\n    keeps you GST compliant, automates business workflows, and helps you\n    work collectively across departments.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "app_url": "https://www.zoho.com/us/books/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.179Z", "updated_at": "2025-05-15T09:26:37.179Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "zoho_inventory", "name": "Zoho inventory", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Zoho Inventory is an online inventory management software that helps\n    businesses manage their inventory, sales, and purchases efficiently.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "app_url": "https://www.zoho.com/us/inventory/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.179Z", "updated_at": "2025-05-15T09:26:37.179Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "adobe", "name": "Adobe", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/adobe.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Adobe provides a range of software and services for digital media and\n    digital marketing.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/adobe.svg", "app_url": "https://get.adobe.com/reader/otherversions", "categories": [{"id": "design-&-creative-tools", "name": "design & creative tools"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "facebook", "name": "Facebook", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/facebook.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Facebook is a social media and social networking service.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/facebook.svg", "app_url": "https://www.facebook.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "tinypng", "name": "Tinypng", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tinypng-logo.png", "authConfigMode": ["API_KEY", "BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "TinyPNG provides APIs for compressing and optimizing WebP, JPEG and\n    PNG images.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tinypng-logo.png", "app_url": "https://tinypng.com", "categories": [{"id": "design-&-creative-tools", "name": "design & creative tools"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY", "BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "mopinion", "name": "Mopinion", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mopinion.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Mopinion to collect and analyze customer feedback, enhancing\n    user experience and driving business insights.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mopinion.jpeg", "app_url": "https://www.mopinion.com", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}, {"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "crustdata", "name": "Crustdata", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/crustdata.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 14, "description": "CrustData is an AI-powered data intelligence platform that provides real-time company and people data via APIs and webhooks, empowering B2B sales teams, AI SDRs, and investors to act on live signals", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/crustdata.png", "app_url": "https://crustdata.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "lever_sandbox", "name": "Lever sandbox", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//lever.png", "authConfigMode": ["API_KEY", "OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Lever provides APIs for applicant tracking, candidate relationship\n    management, and talent acquisition.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//lever.png", "app_url": "https://www.lever.co", "categories": [{"id": "hr-&-recruiting", "name": "hr & recruiting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY", "OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "front", "name": "Front", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/front.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "A customer communication hub that brings emails and messages into a\n    single platform for better team collaboration and customer service.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/front.svg", "app_url": "https://frontapp.com", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "webex", "name": "Webex", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/webex.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Webex to enable communication, collaboration, and meetings\n    through messaging, video conferencing, and more.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/webex.png", "app_url": "https://www.webex.com/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "brandfetch", "name": "Brandfetch", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/brandfetch-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Brandfetch is the home for the world’s brands. Discover the latest\n    logos, colors, fonts and more.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/brandfetch-logo.png", "app_url": "https://brandfetch.com", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "canva", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/canva.jpeg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 32, "description": "Canva offers a drag-and-drop design suite for creating social media graphics, presentations, and marketing materials with prebuilt templates and a vast element library", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/canva.jpeg", "app_url": "https://www.canva.com", "categories": [{"id": "design-&-creative-tools", "name": "design & creative tools"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "digicert", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/digicert.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "DigiCert provides SSL, IoT and other PKI solutions for securing web\n    applications, data and connections.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/digicert.svg", "app_url": "https://www.digicert.com/", "categories": [{"id": "security-&-compliance", "name": "security & compliance"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "factorial", "name": "Factorial", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//factorial.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "It is a comprehensive software solution designed to streamline and\n    manage various HR processes within an organization.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//factorial.svg", "app_url": "https://factorialhr.com", "categories": [{"id": "hr-&-recruiting", "name": "hr & recruiting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "amazon", "name": "Amazon", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//amazon.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Amazon is an American multinational technology company, engaged in\n    e-commerce, cloud computing, online advertising, digital streaming,\n    and artificial intelligence.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//amazon.svg", "app_url": "https://www.amazon.in/", "categories": [{"id": "e-commerce", "name": "e-commerce"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "dailybot", "name": "Dailybot", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//dailybot.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "DailyBot provides APIs for building custom integrations and add-ons\n    for the DailyBot platform.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//dailybot.jpg", "app_url": "https://www.dailybot.com/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}, {"id": "workflow-automation", "name": "workflow automation"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "vero", "name": "Vero", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//vero.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Vero is a customer engagement platform for product and product\n    marketing teams at B2C and product-led B2B businesses.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//vero.png", "app_url": "https://vero.co/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "terminus", "name": "Terminus", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//terminus.jpeg", "authConfigMode": ["BASIC", "API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Terminus provides a RESTful API for performing various operations on\n    your Terminus account, such as managing projects, conventions, and\n    information fields.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//terminus.jpeg", "app_url": "https://termius.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}, {"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["BASIC", "API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "ravenseotools", "name": "Ravenseotools", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ravenseotools.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Raven SEO Tools offers advanced SEO analysis, reporting, and data\n    tracking services, enabling marketers and SEO professionals to enhance\n    their online visibility and marketing strategies.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ravenseotools.svg", "app_url": "https://raventools.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "linkhut", "name": "Linkhu<PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linkhut.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 5, "description": "LinkHut manages bookmarked links in a minimalistic, shareable interface, helping teams organize URLs and track references in one place", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linkhut.svg", "app_url": "https://linkhut.org/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "idea_scale", "name": "Idea scale", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ideascale.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate IdeaScale to access and manage innovation and idea\n    management solutions for your organization.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ideascale.png", "app_url": "https://ideascale.com", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "dropbox_sign", "name": "Dropbox sign", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//hellosign-logo.png", "authConfigMode": ["API_KEY", "OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Dropbox Sign provides APIs for electronic signature, document\n    management, and workflow automation.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//hellosign-logo.png", "app_url": "https://sign.dropbox.com", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY", "OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "wiz", "name": "Wiz", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/wiz.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Wiz to enhance your cloud security posture by identifying\n    and mitigating risks across your cloud infrastructure.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/wiz.png", "app_url": "https://www.wiz.io/", "categories": [{"id": "security-&-compliance", "name": "security & compliance"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "visme", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/visme.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate <PERSON><PERSON><PERSON> to create, collaborate on, and publish stunning visual\n    content, including presentations, infographics, and reports.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/visme.png", "app_url": "https://www.visme.co​", "categories": [{"id": "design-&-creative-tools", "name": "design & creative tools"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "aero_workflow", "name": "Aero workflow", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//aero-workflow-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Workflow designed for accountants by accountants. Aero gives you the\n    tools you need to run your firm proactively, creating a scalable,\n    efficient firm.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//aero-workflow-logo.png", "app_url": "https://www.aeroworkflow.com/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "timely", "name": "Timely", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/timely.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Timely is a time tracking app that helps with scheduling, hours\n    logging, and project planning.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/timely.png", "app_url": "https://www.timelyapp.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "box", "name": "Box", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/box.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 273, "description": "Cloud content management and file sharing service for businesses.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/box.svg", "app_url": "https://www.box.com", "categories": [{"id": "knowledge-base", "name": "knowledge-base"}, {"id": "storage", "name": "storage"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "smugmug", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//smugmug.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "SmugMug is a paid image sharing, image hosting service, and online\n    video platform on which users can upload photos and videos.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//smugmug.svg", "app_url": "https://www.smugmug.com/", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "atlassian", "name": "Atlassian", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/atlassian.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate with Atlassian Cloud products to enhance development tools\n    and collaboration.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/atlassian.svg", "app_url": "https://www.atlassian.com/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "productboard", "name": "Productboard", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//productboard.jpg", "authConfigMode": ["API_KEY", "OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Productboard is a customer-centric product management platform that\n    helps organizations get the right products to market, faster.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//productboard.jpg", "app_url": "https://www.productboard.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY", "OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "blackbaud", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//blackbaud.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Blackbaud, Inc. is a cloud computing provider that serves the social\n    good community—nonprofits, foundations, corporations, education\n    institutions, healthcare organizations, religious organizations, and\n    individual change agents.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//blackbaud.svg", "app_url": "https://www.blackbaud.com/", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "venly", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//venly-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Venly provides APIs and SDKs for integrating blockchain technology\n    into applications, enabling users to interact with blockchain networks\n    without managing their own wallets.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//venly-logo.png", "app_url": "https://www.venly.io", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "qualaroo", "name": "Qualaroo", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//qualaroo.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Qualaroo is a customer and user feedback software that allows you to\n    gather insights from your website visitors and customers.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//qualaroo.jpeg", "app_url": "https://qualaroo.com/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "webflow", "name": "Webflow", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//webflow.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 21, "description": "Webflow is a no-code website design and hosting platform, letting users build responsive sites, launch online stores, and maintain content without coding", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//webflow.jpeg", "app_url": "https://webflow.com", "categories": [{"id": "design-&-creative-tools", "name": "design & creative tools"}, {"id": "e-commerce", "name": "e-commerce"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "amcards", "name": "Amcards", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//amcards.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Amcards offers a variety of digital greeting card solutions, enabling\n    users to create, personalize, and send digital cards for any occasion\n    with ease.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//amcards.svg", "app_url": "https://amcards.com/user/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "simplesat", "name": "Simplesat", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//simplesat.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 2, "description": "Simplesat captures customer feedback and CSAT scores through surveys, integrating directly with helpdesk systems for real-time performance insights", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//simplesat.jpeg", "app_url": "https://www.simplesat.io/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "flutterwave", "name": "Flutterwave", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//flutterwave.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 2, "description": "Flutterwave provides APIs for making and receiving payments in various\n    currencies and countries.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//flutterwave.png", "app_url": "https://flutterwave.com/eu/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}, {"id": "e-commerce", "name": "e-commerce"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "hackerrank_work", "name": "Hackerrank work", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/hackerrank.svg", "authConfigMode": ["BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integration with HackerRank Work for authentication.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/hackerrank.svg", "app_url": "https://www.hackerrank.com/work", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "freshbooks", "name": "Freshbooks", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/freshbooks.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Accounting software that makes running your small business easy, fast\n    and secure.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/freshbooks.svg", "app_url": "https://www.freshbooks.com/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "process_street", "name": "Process street", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//process-street-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 5, "description": "Process Street supports creating and running checklists, SOPs, and workflows, helping teams automate recurring processes and track compliance", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//process-street-logo.png", "app_url": "https://www.process.st", "categories": [{"id": "workflow-automation", "name": "workflow automation"}, {"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "screenshotone", "name": "Screenshotone", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//screenshotone.jpeg", "authConfigMode": ["NO_AUTH"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "ScreenshotOne API is a service that allows users to generate invoices\n    in PDF format for any given URL or HTML, take screenshots of websites\n    with different options, and check that the site looks as expected.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//screenshotone.jpeg", "app_url": "https://screenshotone.com/", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["NO_AUTH"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "chatwork", "name": "Chatwork", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//chatwork-logo.jpg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 8, "description": "Chatwork is a team communication platform featuring group chats, file sharing, and task management, aiming to enhance collaboration and productivity for businesses", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//chatwork-logo.jpg", "app_url": "https://go.chatwork.com", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "klipfolio", "name": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//klipfolio-logo.png", "authConfigMode": ["API_KEY", "BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Connect Composio with Klipfolio to visualize and analyze your data.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//klipfolio-logo.png", "app_url": "https://www.klipfolio.com", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY", "BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "demio", "name": "De<PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//demio-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Demio is built so you can host engaging experiences that turn into\n    analytical insights - making you the most popular marketer in the\n    room.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//demio-logo.png", "app_url": "https://www.demio.com", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//altoviz.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Altoviz is an online management software for entrepreneurs, small\n    businesses, and self-employed individuals. It offers invoicing,\n    payments, treasury, accounting, and more.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//altoviz.jpeg", "app_url": "https://altoviz.com/en/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "d2lbrightspace", "name": "D2lbrightspace", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/d2lbrightspace.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 43, "description": "D2L Brightspace provides APIs for building custom integrations and add-ons\n    for the D2L Brightspace platform.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/d2lbrightspace.png", "app_url": "https://www.d2l.com/brightspace/", "categories": [{"id": "education-&-lms", "name": "education & lms"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "blackboard", "name": "Blackboard", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Blackboard.jpg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 314, "description": "Anthology Adopt powered by Pendo allows institutions to gain insights on Blackboard Learn usage and take action through in-app messages, digital walkthrough guides, and tooltips.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Blackboard.jpg", "app_url": "https://www.blackboard.com", "categories": [{"id": "ed", "name": "ed"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "lever", "name": "Lever", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//lever.png", "authConfigMode": ["API_KEY", "OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Lever provides APIs for applicant tracking, candidate relationship\n    management, and talent acquisition.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//lever.png", "app_url": "https://www.lever.co", "categories": [{"id": "hr-&-recruiting", "name": "hr & recruiting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY", "OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "zoho_bigin", "name": "<PERSON><PERSON>o bigin", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Zoho Bigin is a customer pipeline management software that helps small\n    businesses streamline their customer relationship management\n    processes.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "app_url": "https://www.bigin.com", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.179Z", "updated_at": "2025-05-15T09:26:37.179Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "pandadoc", "name": "Pandadoc", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pandadoc.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 14, "description": "PandaDoc offers document creation, e-signatures, and workflow automation, helping sales teams and businesses streamline proposals, contracts, and agreement processes", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/pandadoc.svg", "app_url": "https://www.pandadoc.com/", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "workiom", "name": "Workiom", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//workiom.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 3, "description": "Workiom provides APIs for automating workflows, integrating with\n    various tools, and building custom applications.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//workiom.jpeg", "app_url": "https://workiom.com", "categories": [{"id": "workflow-automation", "name": "workflow automation"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "lexoffice", "name": "Lexoffice", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//lexoffice-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Lexoffice provides a comprehensive suite of accounting and financial\n    management tools, enabling efficient data processing and management\n    through its API.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//lexoffice-logo.png", "app_url": "https://office.lexware.de/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "browserhub", "name": "Browserhub", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/browserhub.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "BrowserHub offers powerful web scraping and automation capabilities\n    through its API, allowing for scalable data extraction and browser\n    automation.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/browserhub.svg", "app_url": "https://browserhub.io/", "categories": [{"id": "workflow-automation", "name": "workflow automation"}, {"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "fitbit", "name": "Fitbit", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//fitbit-logo.jpg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Fitbit is a line of wireless-enabled wearable technology, physical\n    fitness monitors and activity trackers such as smartwatches,\n    pedometers and monitors for heart rate, quality of sleep, and stairs\n    climbed as well as related software.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//fitbit-logo.jpg", "app_url": "https://www.fitbit.com/dev", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "keap", "name": "Keap", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/keap.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Keap helps you grow your business, improve customer service & increase\n    sales.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/keap.svg", "app_url": "https://keap.com", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}, {"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//gorgias.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 32, "description": "Integration for Gorgias, focusing on e-commerce enhancements.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//gorgias.png", "app_url": "https://www.gorgias.com", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "google_analytics", "name": "Google analytics", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/googleanalytics.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Google Analytics to access your website data, analyze\n    traffic and user behavior, and create reports.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/googleanalytics.png", "app_url": "https://analytics.google.com", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "todoist", "name": "Todoist", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/todoist.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 4, "description": "Todoist is a productivity app that helps manage tasks and projects.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/todoist.svg", "app_url": "https://www.todoist.com/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "zoho_desk", "name": "Zoho desk", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Zoho Desk is a context-aware help desk software that helps businesses\n    focus on the customer. It's convenient to manage customer support\n    tickets, automate processes, and analyze operations.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "app_url": "https://www.zoho.com/desk", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.179Z", "updated_at": "2025-05-15T09:26:37.179Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "battlenet", "name": "Battlenet", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//battlenet.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Battle.net is an Internet-based online game, social networking\n    service, digital distribution, and digital rights management platform\n    developed by Blizzard Entertainment.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//battlenet.png", "app_url": "https://us.shop.battle.net/en-us#optLogin=true", "categories": [{"id": "entertainment-&-media", "name": "entertainment & media"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "ashby", "name": "Ashby", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ashby.svg", "authConfigMode": ["BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate with <PERSON><PERSON> to manage your recruitment process seamlessly.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ashby.svg", "app_url": "https://www.ashbyhq.com", "categories": [{"id": "hr-&-recruiting", "name": "hr & recruiting"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "datarobot", "name": "Datarobot", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//datarobot.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "API for interacting with DataRobot services", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//datarobot.png", "app_url": "https://www.datarobot.com/", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "ngrok", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//ngrok-logo.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 9, "description": "Ngrok creates secure tunnels to locally hosted applications, enabling developers to share and test webhooks or services without configuring complex network settings", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//ngrok-logo.jpeg", "app_url": "https://ngrok.com/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "precoro", "name": "Precoro", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/precoro.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Precoro to streamline and manage your procurement,\n    purchasing, and expense management processes.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/precoro.jpeg", "app_url": "https://precoro.com/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "square", "name": "Square", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//squareup.svg", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Square is a comprehensive commerce platform that offers tools for\n    payment processing, point-of-sale, and online store management.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//squareup.svg", "app_url": "https://squareup.com", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}, {"id": "e-commerce", "name": "e-commerce"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "yandex", "name": "Yandex", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//yandex.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Yandex is a popular search engine that serves as an alternative to\n    Google search.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//yandex.svg", "app_url": "https://yandex.com/", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "baserow", "name": "Baserow", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//baserow-logo.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 2, "description": "Baserow is an open-source database tool that lets teams build no-code data applications, collaborate on records, and integrate with other services for data management", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//baserow-logo.jpeg", "app_url": "https://baserow.io/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "dialpad", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/dialpad.png", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 192, "description": "Dialpad is a cloud-based business phone system and contact center platform that enables voice, video, messages and meetings across your existing devices", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/dialpad.png", "app_url": "https://www.dialpad.com/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "formsite", "name": "Formsite", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//formsite.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 5, "description": "Formsite helps users create online forms and surveys with drag-and-drop tools, secure data capture, and integrations to simplify workflows", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//formsite.png", "app_url": "https://www.formsite.com/", "categories": [{"id": "workflow-automation", "name": "workflow automation"}, {"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "ynab", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//ynab.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "YNAB (You Need A Budget) is a personal finance software that helps\n    users manage their budget and track their expenses. The YNAB API\n    allows developers to build applications that interact with YNAB budget\n    data.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//ynab.svg", "app_url": "https://www.ynab.com", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "kommo", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/kommo.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 15, "description": "Kommo CRM (formerly amoCRM) integration tool for managing customer relationships,\n    sales pipelines, and business processes. This tool enables automation of various\n    CRM operations including:", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/kommo.png", "app_url": "https://www.kommo.com", "categories": [{"id": "crm", "name": "crm"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "deel", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//deel.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Deel is the all-in-one HR platform for global teams. It helps\n    companies simplify every aspect of managing an international\n    workforce, from culture and onboarding, to local payroll and\n    compliance.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//deel.svg", "app_url": "https://www.deel.com/", "categories": [{"id": "hr-&-recruiting", "name": "hr & recruiting"}, {"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "tisane", "name": "Tisane", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tisane_ai.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Tisane.ai provides APIs for natural language processing, text\n    analysis, and content moderation in multiple languages.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tisane_ai.png", "app_url": "https://tisane.ai/", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "coinmarketcal", "name": "Coinmarketcal", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//coinmarketcal.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "CoinMarketCal provides a comprehensive calendar of cryptocurrency\n    events, including token sales, exchange listings, roadmap milestones,\n    and more.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//coinmarketcal.png", "app_url": "https://coinmarketcal.com/en/", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "browsea<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/browseai.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 9, "description": "Browse.ai allows you to turn any website into an API using its advanced web automation and data extraction tools, enabling easy monitoring and data retrieval from websites.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/browseai.svg", "app_url": "https://www.browseai.com", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}, {"id": "workflow-automation", "name": "workflow automation"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "maintainx", "name": "Maintainx", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//maintainx-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "MaintainX provides a comprehensive suite of tools for maintenance and\n    operations management, enabling businesses to streamline workflows,\n    enhance communication, and improve operational efficiency.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//maintainx-logo.png", "app_url": "https://www.getmaintainx.com", "categories": [{"id": "workflow-automation", "name": "workflow automation"}, {"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tinyurl-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "TinyURL provides APIs for shortening long URLs into compact, branded\n    links and tracking link analytics.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tinyurl-logo.png", "app_url": "https://www.tinyurl.com", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "bitwarden", "name": "Bitwarden", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//bitwarden.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Bitwarden makes it easy for businesses and individuals to securely\n    generate, store, and share passwords from any location, browser, or\n    device.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//bitwarden.png", "app_url": "https://bitwarden.com/", "categories": [{"id": "security-&-compliance", "name": "security & compliance"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "epic_games", "name": "Epic games", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/epic-games.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Epic Games integration for OAuth2 authentication.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/epic-games.svg", "app_url": "https://store.epicgames.com/en-US/", "categories": [{"id": "entertainment-&-media", "name": "entertainment & media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "ncscale", "name": "Ncscale", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//ncscale.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "ncScale provides a robust data API for programmatically interacting\n    with and manipulating ncScale data.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//ncscale.jpeg", "app_url": "https://www.ncscale.com", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "timecamp", "name": "Timecamp", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/timecamp.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "TimeCamp provides APIs for time tracking, project management, and\n    productivity analytics", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/timecamp.png", "app_url": "https://www.timecamp.com", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "tapform", "name": "Tapform", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tapform.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Tapform provides a platform for secure data form management and\n    integration, enabling users to easily capture and integrate data\n    across various applications.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tapform.png", "app_url": "https://tapform.io/", "categories": [{"id": "workflow-automation", "name": "workflow automation"}, {"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "braintree", "name": "Braintree", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//braintree.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Braintree is a Chicago-based company that primarily deals in mobile\n    and web payment systems for e-commerce companies.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//braintree.svg", "app_url": "https://www.paypal.com/us/braintree?utm_campaign=Braintree_Migration&utm_medium=Website&utm_source=Braintree", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "piggy", "name": "Piggy", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//piggy.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Piggy is a cashback and loyalty platform that helps businesses reward\n    their customers for purchases and engagement.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//piggy.png", "app_url": "https://www.piggy.eu/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "ring_central", "name": "Ring central", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ring-central.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "RingCentral provides cloud-based communication and collaboration\n    solutions for businesses.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ring-central.svg", "app_url": "https://www.ringcentral.com/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "alchemy", "name": "Alchemy", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tapformio.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 7, "description": "Alchemy is a blockchain development platform that provides powerful APIs and developer tools for building and scaling Ethereum applications", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//tapformio.png", "app_url": "https://www.alchemy.com/", "categories": [{"id": "blockchain", "name": "blockchain"}, {"id": "web3", "name": "web3"}, {"id": "ethereum", "name": "ethereum"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "guru", "name": "<PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/guru.svg", "authConfigMode": ["BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Guru brings your team's knowledge, docs, and projects together in one\n    place.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/guru.svg", "app_url": "https://www.guru.com/", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "gumroad", "name": "Gumroad", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gumroad.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Gumroad helps creators take control of their creative careers by\n    providing tools to sell products directly to their audience.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gumroad.svg", "app_url": "https://gumroad.com", "categories": [{"id": "e-commerce", "name": "e-commerce"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "appsflyer", "name": "Appsflyer", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/appsflyer.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate AppsFlyer to track mobile app performance, analyze marketing\n    campaigns, and optimize user acquisition.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/appsflyer.png", "app_url": "https://www.appsflyer.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "seismic", "name": "Seismic", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sesmic.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Seismic to empower your sales teams with enablement tools,\n    content automation, and analytics.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sesmic.png", "app_url": "https://seismic.com/", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "foursquare", "name": "Foursquare", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/foursquare.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 5, "description": "Search for places and place recommendations from the Foursquare Places database", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/foursquare.png", "app_url": "https://www.foursquare.com", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "go_to_webinar", "name": "Go to webinar", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gotowebinar.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate GoToWebinar to manage and host webinars, track attendance,\n    and engage with your audience.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gotowebinar.png", "app_url": "https://gotowebinar.com", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "open_sea", "name": "Open sea", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/opensea.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 22, "description": "OpenSea is the world's first and largest NFT marketplace for NFTs and crypto collectibles.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/opensea.png", "app_url": "https://opensea.io", "categories": [{"id": "nft", "name": "nft"}, {"id": "web3", "name": "web3"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "customer_io", "name": "Customer io", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//customer.io-logo.png", "authConfigMode": ["API_KEY", "BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Customer.io provides APIs for managing customer data and sending\n    personalized messages.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//customer.io-logo.png", "app_url": "https://customer.io", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY", "BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "humanloop", "name": "Humanloop", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//humanloop.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 4, "description": "Humanloop helps developers build and refine AI applications, offering user feedback loops, model training, and data annotation to iterate on language model performance", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//humanloop.jpeg", "app_url": "https://humanloop.com", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "auth0", "name": "Auth0", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//Auth0.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Auth0 is a platform that allows developers and companies to verify a\n    user's identity before giving them access to applications and\n    websites.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//Auth0.png", "app_url": "https://auth0.com", "categories": [{"id": "security-&-compliance", "name": "security & compliance"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "zoominfo", "name": "Zoominfo", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/agencyzoom_logo.jpeg", "authConfigMode": ["BASIC_WITH_JWT"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 14, "description": "ZoomInfo is a multiplatform operating system that revenue teams use to deliver business growth.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/agencyzoom_logo.jpeg", "app_url": "https://www.zoominfo.com/", "categories": [{"id": "crm", "name": "crm"}], "created_at": "2025-05-15T09:26:37.179Z", "updated_at": "2025-05-15T09:26:37.179Z"}, "auth_schemes": ["BASIC_WITH_JWT"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "gong", "name": "<PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gong.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 54, "description": "Gong is a platform for video meetings, call recording, and team collaboration.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gong.png", "app_url": "https://www.gong.io", "categories": [{"id": "collaboration", "name": "collaboration"}, {"id": "communication", "name": "communication"}, {"id": "scheduling", "name": "scheduling"}, {"id": "project-management", "name": "project-management"}, {"id": "analytics", "name": "analytics"}, {"id": "marketing", "name": "marketing"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "placekey", "name": "Placekey", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//placekey.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 3, "description": "Placekey standardizes location data by assigning unique IDs to physical addresses, simplifying address matching and enabling data sharing across platforms", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//placekey.png", "app_url": "https://www.placekey.io/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "lastpass", "name": "Lastpass", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//lastpass-logo.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "LastPass is a password manager that stores encrypted passwords online.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//lastpass-logo.png", "app_url": "https://www.lastpass.com", "categories": [{"id": "security-&-compliance", "name": "security & compliance"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "datagma", "name": "Datagma", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//datagma-logo.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Composio with Datagma for data analysis and insights.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//datagma-logo.jpeg", "app_url": "https://datagma.com/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "servicem8", "name": "Servicem8", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/servicem8.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "ServiceM8 is a cloud-based solution that helps field service\n    businesses manage their operations, from job scheduling to invoicing.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/servicem8.svg", "app_url": "https://www.servicem8.com", "categories": [{"id": "workflow-automation", "name": "workflow automation"}, {"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "microsoft_tenant", "name": "Microsoft tenant", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//microsoft-tenant-logo.jpeg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Microsoft Tenant is a proprietary business communication platform\n    developed by Microsoft, as part of the Microsoft 365 family of\n    products.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//microsoft-tenant-logo.jpeg", "app_url": "https://learn.microsoft.com/en-us/microsoft-365/solutions/tenant-management-overview?view=o365-worldwide", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "smartrecruiters", "name": "Smartrecruiters", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//smartrecruiters-api-key.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Hiring Without Boundaries™ Move Faster and <PERSON><PERSON> with a Modern,\n    All-in-One Hiring Platform.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//smartrecruiters-api-key.svg", "app_url": "https://www.smartrecruiters.com", "categories": [{"id": "hr-&-recruiting", "name": "hr & recruiting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "textrazor", "name": "Textrazor", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/textrazor.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 12, "description": "TextRazor is a natural language processing API that extracts meaning, entities, and relationships from text, powering advanced content analysis and sentiment detection", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/textrazor.svg", "app_url": "https://www.textrazor.com", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "bubble", "name": "Bubble", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//bubble-logo.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Bubble is a visual programming language and a PaaS developed by Bubble\n    Group.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//bubble-logo.jpg", "app_url": "https://bubble.io/", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "sage", "name": "Sage", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sage.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Sage provides cloud accounting and financial management solutions for\n    businesses.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/sage.svg", "app_url": "https://www.sage.com/en-gb/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "chmeetings", "name": "Ch<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/chmeetings.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "CHMeetings is a comprehensive church management platform offering\n    solutions for event planning, member engagement, and administrative\n    efficiency.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/chmeetings.svg", "app_url": "https://www.chmeetings.com/", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "cloudflare", "name": "Cloudflare", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//cloudflare.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Cloudflare provides APIs for managing and automating Cloudflare\n    services, including DNS, CDN, security, and more.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//cloudflare.png", "app_url": "https://www.cloudflare.com/", "categories": [{"id": "security-&-compliance", "name": "security & compliance"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "harvest", "name": "Harvest", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/harvest.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Harvest makes it easy to track time across all your projects and turn\n    that data into reporting or invoices.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/harvest.png", "app_url": "https://www.getharvest.com/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}, {"id": "scheduling-&-booking", "name": "scheduling & booking"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "wakatime", "name": "Wakatime", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//wakatime.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "WakaTime is a personal analytics service for programmers that shows\n    you how you spend your time and helps you be more productive.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//wakatime.svg", "app_url": "https://wakatime.com/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "xero", "name": "Xero", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//xero.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Xero online accounting software for your business connects you to your\n    bank, accountant, bookkeeper, and other business apps.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//xero.svg", "app_url": "https://www.xero.com/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "boldsign", "name": "Boldsign", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//boldsign.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "BoldSign is an easy-to-use and secure electronic signature solution.\n    With BoldSign you can quickly complete your contracts, accept offers,\n    sign NDAs, approve invoices, confirm service appointments, onboard new\n    employees, and more with just few clicks.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//boldsign.svg", "app_url": "https://boldsign.com/", "categories": [{"id": "document-&-file-management", "name": "document & file management"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "accelo", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//accelo.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "A system to boost productivity and profitability across your service\n    business.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//accelo.svg", "app_url": "https://www.accelo.com/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}, {"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "moxie", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/moxie.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Moxie provides APIs for managing clients, contacts, projects,\n    invoices, expenses and more.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/moxie.png", "app_url": "https://moxiebeauty.in", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}, {"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}, {"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "brightpearl", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/brightpearl.png", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Brightpearl to manage your business operations, including\n    orders, inventory, accounting, and more.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/brightpearl.png", "app_url": "https://www.brightpearl.com", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}, {"id": "e-commerce", "name": "e-commerce"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "rippling", "name": "Rippling", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Rippiling.jpeg", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integrate Rippling to manage HR, IT, payroll, and more from a unified\n    platform.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Rippiling.jpeg", "app_url": "https://www.rippling.com/", "categories": [{"id": "hr-&-recruiting", "name": "hr & recruiting"}, {"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "active_campaign", "name": "Active campaign", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//activecampaign.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 7, "description": "ActiveCampaign provides APIs for marketing automation, customer\n    relationship management (CRM), and email marketing.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//activecampaign.png", "app_url": "https://www.activecampaign.com", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}, {"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "zoho_mail", "name": "Zoho mail", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Zoho Mail is an email hosting service built with the needs of a modern business in mind.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "app_url": "https://www.zoho.com/mail/", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.179Z", "updated_at": "2025-05-15T09:26:37.179Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "mural", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mural.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "MURAL is a digital workspace for visual collaboration.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/mural.svg", "app_url": "https://www.mural.co", "categories": [{"id": "design-&-creative-tools", "name": "design & creative tools"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "brex", "name": "Brex", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//brex-staging-logo.png", "authConfigMode": ["OAUTH2", "API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Integration for Brex banking services using OAuth2 authentication.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//brex-staging-logo.png", "app_url": "https://www.brex.com/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2", "API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "intercom", "name": "Intercom", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/intercom.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 43, "description": "A messaging platform that allows businesses to communicate with\n    prospective and existing customers within their app, on their website,\n    through social media, or via email.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/intercom.svg", "app_url": "https://www.intercom.com", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}, {"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "highlevel", "name": "Highlevel", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/highlevel.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Highlevel is a messaging platform that allows businesses to\n    communicate with prospective and existing customers within their app,\n    on their website, through social media, or via email.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/highlevel.svg", "app_url": "https://www.gohighlevel.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "axonaut", "name": "Axonaut", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//axonaut-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Axonaut, the management software for  VSEs and SMEs", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//axonaut-logo.png", "app_url": "https://axonaut.com/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}, {"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "eventbrite", "name": "Eventbrite", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/eventbrite.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "An event management and ticketing website.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/eventbrite.svg", "app_url": "https://www.eventbrite.com/", "categories": [{"id": "marketing-&-social-media", "name": "marketing & social media"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "wave_accounting", "name": "Wave accounting", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//wave-accounting.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Wave is a company that provides financial services and software for\n    small businesses.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//wave-accounting.svg", "app_url": "https://www.waveapps.com", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "beeminder", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//beeminder-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Beeminder functions as a self-tracking tool and provides a visual\n    representation of progress towards predefined objectives.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//beeminder-logo.png", "app_url": "https://www.beeminder.com/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "printnode", "name": "Printnode", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//printnode-logo.jpeg", "authConfigMode": ["BASIC"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Connect Composio with PrintNode to manage and control your printers.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//printnode-logo.jpeg", "app_url": "https://www.printnode.com/en", "categories": [{"id": "other-/-miscellaneous", "name": "other / miscellaneous"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["BASIC"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "rocket_reach", "name": "Rocket reach", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//rocketreach.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "RocketReach provides APIs for searching and retrieving contact\n    information for professionals and companies.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//rocketreach.png", "app_url": "https://www.rocketreach.co", "categories": [{"id": "crm", "name": "crm"}, {"id": "sales-&-customer-support", "name": "sales & customer support"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "interzoid", "name": "Interzoid", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//interzoid.jpeg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Interzoid provides AI-enhanced data matching solutions, including a\n    comprehensive API-first data platform, interactive web applications,\n    and seamless integration with cloud data sources.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//interzoid.jpeg", "app_url": "https://www.interzoid.com/", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "exist", "name": "Exist", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//exist.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "With Exist, you can create your own data points to track anything else\n    you want. Use it as a habit tracker, keep a record of medications and\n    symptoms, or track subjective measures like energy and stress levels.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//exist.svg", "app_url": "https://exist.io/", "categories": [{"id": "productivity-&-project-management", "name": "productivity & project management"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "zenserp", "name": "Zenserp", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zenserp.jpg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "ZenSERP provides APIs for search engine result pages (SERP) data,\n    including organic results, ads, related searches, and more.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zenserp.jpg", "app_url": "https://zenserp.com", "categories": [{"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "zoho_invoice", "name": "Zoho invoice", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Zoho Invoice is online invoicing software that helps you craft\n    beautiful invoices, automatically send payment reminders and get paid\n    faster online.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//zoho.png", "app_url": "https://www.zoho.com/us/invoice/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.179Z", "updated_at": "2025-05-15T09:26:37.179Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "timekit", "name": "Timekit", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/timekit.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Timekit provides APIs for building customizable booking and scheduling\n    solutions.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/timekit.svg", "app_url": "https://www.timekit.io/", "categories": [{"id": "scheduling-&-booking", "name": "scheduling & booking"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "stack_exchange", "name": "Stack exchange", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/stackexchange.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Stack Exchange is a network of question-and-answer websites on topics\n    in diverse fields, each site covering a specific topic, where\n    questions, answers, and users are subject to a reputation award\n    process.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/stackexchange.png", "app_url": "https://www.stackexchange.com", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "botbaba", "name": "Botbaba", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//botbaba.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Botbaba provides APIs for building and managing chatbots, including\n    conversational AI and messaging integrations.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//botbaba.png", "app_url": "https://botbaba.io​", "categories": [{"id": "ai-&-machine-learning", "name": "ai & machine learning"}], "created_at": "2025-05-15T09:26:37.176Z", "updated_at": "2025-05-15T09:26:37.176Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "twitch", "name": "Twitch", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/twitch.avif", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": ["OAUTH2"], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Twitch is a live streaming platform focused on video gaming, esports,\n    and creative content.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/twitch.avif", "app_url": "https://www.twitch.tv/", "categories": [{"id": "entertainment-&-media", "name": "entertainment & media"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": ["OAUTH2"], "is_local_toolkit": false, "no_auth": false}, {"slug": "datadog", "name": "Datadog", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//datadog-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Datadog provides APIs for monitoring and analyzing application\n    performance, infrastructure, and logs.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//datadog-logo.png", "app_url": "https://www.datadoghq.com", "categories": [{"id": "developer-tools-&-devops", "name": "developer tools & devops"}, {"id": "analytics-&-data", "name": "analytics & data"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "waboxapp", "name": "Waboxapp", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//waboxapp-logo.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 0, "description": "Waboxapp provides APIs for sending messages, images, and media files\n    through WhatsApp.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master//waboxapp-logo.png", "app_url": "https://www.waboxapp.com", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "echtpost", "name": "Echtpost", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/echtpost.svg", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 1, "description": "EchtPost facilitates secure digital communication, encryption, and data privacy, providing a reliable channel for sending confidential documents and messages", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/echtpost.svg", "app_url": "https://echtpost.de/login", "categories": [{"id": "collaboration-&-communication", "name": "collaboration & communication"}, {"id": "security-&-compliance", "name": "security & compliance"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "bugbug", "name": "Bugbug", "enabled": true, "logo": "some_logo_url", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 4, "description": "Template description for bugbug", "logo": "some_logo_url", "app_url": "https://bugbug.io​", "categories": [{"id": "some-category", "name": "some category"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "excel", "name": "Excel", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Excel.png", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 25, "description": "Connect to Excel to create and manage spreadsheets.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/Excel.png", "app_url": "https://excel.cloud.microsoft/en-us/", "categories": [{"id": "data-&-analytics", "name": "data & analytics"}], "created_at": "2025-05-15T09:26:37.177Z", "updated_at": "2025-05-15T09:26:37.177Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "quickbooks", "name": "Quickbooks", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/quickbooks.jpg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 13, "description": "Quickbooks is a cloud-based accounting software that helps you manage your finances, track your income and expenses, and get insights into your business", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/quickbooks.jpg", "app_url": "https://quickbooks.intuit.com", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "ramp", "name": "<PERSON><PERSON>", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ramp.svg", "authConfigMode": ["OAUTH2"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 8, "description": "Ramp is a comprehensive finance platform designed to help you manage\n    your finances, track your income and expenses, and get insights into\n    your business. The Ramp API provides developers with the tools to\n    interact with the platform programmatically.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/ramp.svg", "app_url": "https://ramp.com/", "categories": [{"id": "finance-&-accounting", "name": "finance & accounting"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["OAUTH2"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "resend", "name": "Resend", "enabled": true, "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/resend.png", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 18, "description": "Connect to Resend to send emails.", "logo": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/resend.png", "app_url": "https://resend.com", "categories": [{"id": "email", "name": "email"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "rootly", "name": "<PERSON>ly", "enabled": true, "logo": "some_logo_url", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 3, "description": "Template description for rootly", "logo": "some_logo_url", "categories": [{"id": "some-category", "name": "some category"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}, {"slug": "scrape_do", "name": "Scrape do", "enabled": true, "logo": "some_logo_url", "authConfigMode": ["API_KEY"], "composioManagedAuthConfigs": [], "isLocalToolkit": false, "noAuth": false, "meta": {"triggers_count": 0, "tools_count": 4, "description": "Template description for scrape_do", "logo": "some_logo_url", "categories": [{"id": "some-category", "name": "some category"}], "created_at": "2025-05-15T09:26:37.178Z", "updated_at": "2025-05-15T09:26:37.178Z"}, "auth_schemes": ["API_KEY"], "composio_managed_auth_schemes": [], "is_local_toolkit": false, "no_auth": false}], "next_cursor": null, "total_pages": 1}